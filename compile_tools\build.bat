@echo off
REM Compile Tools - Windows Build Script
REM ====================================

echo.
echo ========================================
echo    Compile Tools - Windows Build
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo [1/6] Checking Python installation...
python --version

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    pause
    exit /b 1
)

echo [2/6] Installing/Updating dependencies...
pip install -r requirements-build.txt

if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [3/6] Cleaning previous build...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo [4/6] Building standard version...
pyinstaller --onefile --windowed main.py --name CompileTools

if errorlevel 1 (
    echo ERROR: Standard version build failed
    pause
    exit /b 1
)

echo [5/6] Building standalone version...
pyinstaller --onefile --windowed --collect-all PySide6 --collect-all paramiko --collect-all cryptography --hidden-import=sqlite3 --hidden-import=select --hidden-import=stat main.py --name CompileTools_Standalone

if errorlevel 1 (
    echo WARNING: Standalone version build failed, but standard version is available
)

echo [6/6] Checking build results...
if not exist "dist\CompileTools.exe" (
    echo ERROR: CompileTools.exe was not created
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo ========================================
echo Build completed!
echo.
echo Standard version: dist\CompileTools.exe
if exist "dist\CompileTools_Standalone.exe" (
    echo Standalone version: dist\CompileTools_Standalone.exe
)
echo.
echo File sizes:
for %%A in ("dist\CompileTools.exe") do echo   CompileTools.exe: %%~zA bytes
if exist "dist\CompileTools_Standalone.exe" (
    for %%A in ("dist\CompileTools_Standalone.exe") do echo   CompileTools_Standalone.exe: %%~zA bytes
)
echo.
echo You can now run: dist\CompileTools.exe
echo ========================================
echo.

pause
