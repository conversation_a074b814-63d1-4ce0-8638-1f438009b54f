@echo off
echo ========================================
echo  Compile Tools - Standalone Build
echo ========================================
echo.
echo This will create a completely self-contained executable
echo that can run on machines without Python installed.
echo.

echo [1/4] Installing dependencies...
pip install PyInstaller PySide6 paramiko cryptography

echo.
echo [2/4] Cleaning previous build...
if exist "dist\CompileTools_Standalone.exe" del "dist\CompileTools_Standalone.exe"
if exist "build" rmdir /s /q "build"

echo.
echo [3/4] Building standalone executable...
echo This may take several minutes...
pyinstaller --onefile --windowed --collect-all PySide6 --collect-all paramiko --collect-all cryptography --hidden-import=sqlite3 --hidden-import=select --hidden-import=stat main.py --name CompileTools_Standalone

echo.
echo [4/4] Verifying build...
if exist "dist\CompileTools_Standalone.exe" (
    echo.
    echo ========================================
    echo SUCCESS: Standalone build completed!
    echo ========================================
    echo.
    echo File: dist\CompileTools_Standalone.exe
    echo.
    echo File size:
    for %%A in ("dist\CompileTools_Standalone.exe") do echo   %%~zA bytes
    echo.
    echo This executable includes:
    echo - Python runtime
    echo - PySide6 GUI framework
    echo - Paramiko SSH library
    echo - All cryptography dependencies
    echo - SQLite database support
    echo.
    echo The file can run on any Windows machine
    echo without requiring Python installation.
    echo.
    echo ========================================
) else (
    echo.
    echo ERROR: Standalone build failed!
    echo CompileTools_Standalone.exe was not created.
    echo.
    echo Possible solutions:
    echo 1. Check if all dependencies are installed
    echo 2. Try running: pip install --upgrade PyInstaller
    echo 3. Check available disk space
    echo 4. Run as administrator if needed
    echo.
)

echo.
echo Press any key to exit...
pause >nul
