@echo off
echo ========================================
echo    Compile Tools - Simple Build
echo ========================================
echo.

echo Installing dependencies...
pip install PyInstaller PySide6 paramiko

echo.
echo Cleaning previous build...
if exist "dist\CompileTools.exe" del "dist\CompileTools.exe"
if exist "build" rmdir /s /q "build"

echo.
echo Building executable...
pyinstaller --onefile --windowed main.py --name CompileTools

echo.
echo Build completed!
if exist "dist\CompileTools.exe" (
    echo Success: dist\CompileTools.exe created
    echo File size:
    for %%A in ("dist\CompileTools.exe") do echo   %%~zA bytes
) else (
    echo Error: CompileTools.exe not found
)

echo.
echo Press any key to exit...
pause >nul
