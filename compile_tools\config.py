"""
Compile Tools - Configuration Module
====================================

This module contains configuration settings for the Compile Tools application.
"""

import os
from pathlib import Path

# Application Information
APP_NAME = "Compile Tools"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Remote Compilation and File Management Tool"

# UI Configuration
UI_THEME = {
    "primary_color": "#007AFF",
    "secondary_color": "#5856D6", 
    "success_color": "#34C759",
    "warning_color": "#FF9500",
    "error_color": "#FF3B30",
    "background_color": "#F2F2F7",
    "text_color": "#000000",
    "border_color": "#C7C7CC"
}

# Window Settings
WINDOW_DEFAULT_SIZE = (1000, 700)
WINDOW_MIN_SIZE = (800, 600)

# Connection Settings
SSH_CONNECTION_TIMEOUT = 10  # seconds
SSH_STATUS_UPDATE_INTERVAL = 5000  # milliseconds
MAX_LOG_LINES = 1000  # Maximum lines in compile log

# File Transfer Settings
DOWNLOAD_CHUNK_SIZE = 8192  # bytes
MAX_CONCURRENT_DOWNLOADS = 3

# Database Settings
DATABASE_NAME = "compile_tools.db"

# Logging Settings
LOG_LEVEL = "INFO"
LOG_FILE = "compile_tools.log"
MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 3

# Security Settings
AUTO_ACCEPT_HOST_KEYS = True  # For development convenience
SESSION_TIMEOUT = 3600  # seconds (1 hour)

# Feature Flags
FEATURES = {
    "auto_refresh_artifacts": True,
    "connection_status_monitoring": True,
    "download_progress_dialog": True,
    "compilation_interruption": True,
    "ssh_connection_pooling": True
}

# Default Values
DEFAULTS = {
    "ssh_port": 22,
    "auth_method": "password",
    "compile_timeout": 300,  # 5 minutes
    "artifact_refresh_after_compile": True,
    "auto_select_single_option": True,  # 当只有一个选项时自动选择
    "use_relative_download_path": True,  # 使用相对下载路径
    "download_root_dir": "downloads",  # 下载根目录
    "default_root_path": "/home/<USER>/yf_menjin/git_release",  # 默认项目根路径
    "default_compile_commands": "./pkg_make.sh all",  # 默认编译命令
    "support_relative_artifact_path": True  # 支持相对artifact路径
}

def get_app_data_dir():
    """Get the application data directory based on the operating system"""
    if os.name == 'nt':  # Windows
        return Path(os.getenv('APPDATA', Path.home() / "AppData" / "Roaming")) / APP_NAME
    elif os.sys.platform == "darwin":  # macOS
        return Path.home() / "Library" / "Application Support" / APP_NAME
    else:  # Linux and other POSIX
        return Path(os.getenv('XDG_DATA_HOME', Path.home() / ".local" / "share")) / APP_NAME

def get_database_path():
    """Get the full path to the database file"""
    # 使用程序当前目录而不是系统数据目录
    from pathlib import Path
    import os
    return Path(os.getcwd()) / DATABASE_NAME

def get_log_file_path():
    """Get the full path to the log file"""
    # 使用程序当前目录而不是系统数据目录
    from pathlib import Path
    import os
    return Path(os.getcwd()) / LOG_FILE

def get_download_root_path():
    """Get the download root directory path"""
    from pathlib import Path
    import os
    # 使用当前工作目录下的downloads文件夹
    return Path(os.getcwd()) / DEFAULTS["download_root_dir"]

def resolve_artifact_path(artifact_path, root_path):
    """
    解析artifact路径，支持相对路径和绝对路径

    Args:
        artifact_path: 用户输入的artifact路径
        root_path: 项目根路径

    Returns:
        解析后的绝对路径
    """
    if not artifact_path:
        return ""

    # 如果是绝对路径，直接返回
    if artifact_path.startswith('/'):
        return artifact_path

    # 如果是相对路径，基于root_path计算绝对路径
    if artifact_path.startswith('./'):
        # 移除 ./ 前缀
        relative_part = artifact_path[2:]
        return f"{root_path.rstrip('/')}/{relative_part}"
    elif not artifact_path.startswith('/'):
        # 普通相对路径
        return f"{root_path.rstrip('/')}/{artifact_path}"

    return artifact_path

# Ensure download directory exists (数据库文件现在在当前目录，不需要特别创建目录)
DOWNLOAD_ROOT_DIR = get_download_root_path()
DOWNLOAD_ROOT_DIR.mkdir(parents=True, exist_ok=True)
