import hashlib

def generate_super_password():
    """
    根据给定的 sn, ds 和 rd 生成超级密码。
    用户需要手动输入 sn 和 rd (均为字符串)。
    """
    # 固定不变的 ds 值
    # 保持与您提供的原始示例一致，ds 为 b"abcdefghijklmnopqrstuvwxyz123456"
    ds = b"abcdefghijklmnopqrstuvwxyz123456"

    # 手动输入 sn (现在作为普通字符串)
    while True:
        sn_input = input("请输入 SN (任意字符串，例如 E03C1CB438B75801 或其他文本): ")
        if sn_input.strip(): # 确保输入不为空白字符串
            sn = sn_input.encode('utf-8') # 将输入的字符串编码为字节
            break
        else:
            print("SN 输入不能为空。")

    # 手动输入 rd (作为普通字符串)
    while True:
        rd_input = input("请输入 RD (任意字符串，例如 7zQYutm6): ")
        if rd_input.strip(): # 确保输入不为空白字符串
            rd = rd_input.encode('utf-8') # 将输入的字符串编码为字节
            break
        else:
            print("RD 输入不能为空。")

    # 拼接所有字节数据
    data = sn + ds + rd

    # SHA256 哈希，得到十六进制字符串
    hash_hex = hashlib.sha256(data).hexdigest()

    # 取最后6个字符作为超级密码
    super_password = hash_hex[-6:]

    print("\n--- 生成结果 ---")
    print("输入的 SN:", sn_input)
    print("固定的 DS:", ds.decode('utf-8')) # 解码 ds 以便显示
    print("输入的 RD:", rd_input)
    print("完整拼接数据 (字节表示):", data) # 显示字节表示，帮助调试
    print("SHA256 哈希值 (HEX):", hash_hex)
    print("生成的超级密码 (最后6位HEX字符):", super_password)

# 调用函数生成超级密码
if __name__ == "__main__":
    generate_super_password()